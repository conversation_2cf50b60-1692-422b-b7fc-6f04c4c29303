<template>
  <div class="px-30 pt-16 mobile-demo-container">
    <div class="f-cer mt-10">
      <motion.div
        :initial="{ opacity: 0, y: 10 }"
        :animate="{ opacity: 1, y: 0 }"
        :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
        class="bg-white-60 dark:bg-[#141415] rounded-15px p-7.5 pb-6 w-730px backdrop-blur border border-white dark:border-[#292929] demo-form-container"
      >
        <Form :validation-schema="schema" @submit="onSubmit">
          <div class="grid grid-cols-2 gap-x-7.5 gap-y-5 demo-form-grid">
            <div class="flex flex-col">
              <label for="name" class="dark:text-[#C6C6C6]">Name</label>
              <Field
                name="name"
                class="input truncate"
                autocomplete="off"
                placeholder="Please enter your name"
                :title="name"
              />
              <ErrorMessage name="name" class="text-red-500 text-sm mt-1" />
            </div>
            <div class="flex flex-col">
              <label for="email" class="dark:text-[#C6C6C6]">Email Address</label>
              <Field
                name="email"
                class="input truncate"
                autocomplete="off"
                placeholder="Please enter your email"
                :title="email"
              />
              <ErrorMessage name="email" class="text-red-500 text-sm mt-1" />
            </div>
            <div class="flex flex-col">
              <label for="affiliation" class="dark:text-[#C6C6C6]">Affiliation</label>
              <Field
                name="affiliation"
                class="input truncate"
                autocomplete="off"
                placeholder="Please enter your affiliation"
                :title="affiliation"
              />
              <ErrorMessage name="affiliation" class="text-red-500 text-sm mt-1" />
            </div>
            <div class="flex flex-col">
              <label for="country" class="dark:text-[#C6C6C6]">Country</label>
              <div class="rel">
                <div
                  class="input appearance-none flex items-center justify-between cursor-pointer"
                  @click="toggleCountryDropdown"
                >
                  <span v-if="selectedCountry" class="dark:text-[#C6C6C6]">{{ selectedCountry.name_en }}</span>
                  <span v-else class="text-gray-400 dark:text-[#7A7A7A]">Please select Country</span>
                  <div class="i-icon-park-outline:down wh-5 text-neutral-200"></div>
                </div>

                <!-- 下拉框 -->
                <div
                  v-if="isCountryDropdownOpen"
                  class="absolute z-10 w-full mt-1 bg-white dark:bg-[#292929] border border-gray-200 dark:border-[#7A7A7A] rounded-md shadow-lg max-h-60 overflow-hidden"
                >
                  <div class="p-2 border-b">
                    <input
                      type="text"
                      v-model="countrySearch"
                      class="w-full px-3 py-1 border rounded-md dark:bg-[#141415] dark:border-[#7A7A7A] dark:text-[#C6C6C6] dark:placeholder-[#7A7A7A]"
                      placeholder="Search countries..."
                      @click.stop
                    />
                  </div>
                  <div ref="containerRef" class="h-60 overflow-auto">
                    <div
                      v-for="item in filteredCountries"
                      :key="item.code"
                      class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-[#7A7A7A] cursor-pointer"
                      :class="{
                        'bg-primary-100/10 text-black dark:bg-[#7A7A7A] dark:text-[#FAF9F5]': selectedCountry?.code === item.code,
                        'text-gray-600 dark:text-[#C6C6C6]': selectedCountry?.code !== item.code,
                      }"
                      @click="selectCountry(item)"
                    >
                      {{ item.name_en }}
                    </div>
                  </div>
                </div>

                <Field name="country" v-slot="{ field }">
                  <input type="hidden" v-bind="field" :value="selectedCountry?.code || ''" />
                </Field>
              </div>
              <ErrorMessage name="country" class="text-red-500 text-sm mt-1" />
            </div>
            <div class="flex flex-col">
              <label for="jobTitle" class="dark:text-[#C6C6C6]">Job title</label>
              <Field
                name="jobTitle"
                class="input truncate"
                autocomplete="off"
                placeholder="Please enter your job title"
                :title="jobTitle"
              />
              <ErrorMessage name="jobTitle" class="text-red-500 text-sm mt-1" />
            </div>
          </div>
          <!-- Reason for contact in single row -->
          <div class="flex flex-col mt-5">
            <label for="ReasonForContact" class="dark:text-[#C6C6C6]">Reason for contact</label>
            <div class="rel">
              <div
                class="input appearance-none flex items-center justify-between cursor-pointer min-h-[42px]"
                @click="toggleReasonDropdown"
              >
                <span v-if="reasonForContact" class="truncate max-w-[calc(100%-20px)] dark:text-[#C6C6C6]">{{
                  getReasonLabel(reasonForContact)
                }}</span>
                <span v-else class="text-gray-400 dark:text-[#7A7A7A]">Please select Reason</span>
                <div class="i-icon-park-outline:down wh-5 text-neutral-200 flex-shrink-0"></div>
              </div>

              <!-- 自定义下拉框 -->
              <div
                v-if="isReasonDropdownOpen"
                class="absolute z-10 w-full mt-1 bg-white dark:bg-[#292929] border border-gray-200 dark:border-[#7A7A7A] rounded-md shadow-lg max-h-60"
              >
                <div class="max-h-60 overflow-auto">
                  <div
                    v-for="(option, index) in reasonOptions"
                    :key="index"
                    class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-[#7A7A7A] cursor-pointer relative group"
                    :class="{
                      'bg-primary-100/10 text-black dark:bg-[#7A7A7A] dark:text-[#FAF9F5]': reasonForContact === option.value,
                      'text-gray-600 dark:text-[#C6C6C6]': reasonForContact !== option.value,
                    }"
                    @click="selectReason(option.value)"
                  >
                    <span class="block whitespace-normal leading-6 text-gray-1000 dark:text-[#C6C6C6]">{{
                      option.label
                    }}</span>
                    <!-- Tooltip -->
                    <div
                      class="hidden group-hover:block fixed transform -translate-x-1/2 left-1/2 -top-32 w-[1000px] bg-gray-800 text-white text-sm px-6 py-4 rounded shadow-lg z-50 whitespace-normal leading-7 mobile-tooltip"
                    >
                      {{ option.label }}
                      <!-- Arrow -->
                      <div
                        class="absolute -bottom-1 left-1/2 w-2 h-2 bg-gray-800 transform -translate-x-1/2 rotate-45"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <Field name="ReasonForContact" v-slot="{ field }">
                <input type="hidden" v-bind="field" :value="reasonForContact" />
              </Field>
            </div>
            <ErrorMessage name="ReasonForContact" class="text-red-500 text-sm mt-1" />
          </div>
          <div class="flex flex-col mt-5">
            <label for="provide" class="dark:text-[#C6C6C6]">
              Provide more details
              <span v-if="isOthersSelected" class="text-red-500">*</span>
              <span v-else>(optional)</span>
            </label>
            <Field
              name="provide"
              as="textarea"
              class="input"
              rows="4"
              placeholder="This is the contents of the textarea."
            />
            <ErrorMessage name="provide" class="text-red-500 text-sm mt-1" />
          </div>
          <div class="fx-cer mt-4">
            <input
              class="agree-checkbox"
              type="checkbox"
              id="checkbox"
              v-model="marketingConsent"
            />
            <label for="checkbox" class="text-sm dark:text-[#C6C6C6] mobile-checkbox-label"
              >I agree to DINQ sending marketing communications about DINQ (optional)</label
            >
          </div>
          <button type="submit" class="submit-btn">Submit Form</button>
        </Form>
      </motion.div>
    </div>
    
    <!-- 成功提交弹窗 -->
    <SuccessModal 
      v-model:visible="showSuccessModal" 
      @close="handleSuccessClose"
    />
    
    <!-- 登录模态框 -->
    <AuthModal 
      v-model:visible="showAuthModal"
      :loading="authLoading"
      @auth="handleAuth"
    />
  </div>
</template>

<script setup lang="ts">
  import { Form, Field, ErrorMessage } from 'vee-validate'
  import { loginSchema } from '@/config/schema'
  import { motion } from 'motion-v'
  import { ref, watch, onMounted, computed, onUnmounted } from 'vue'
  import * as yup from 'yup'
  import { getDemoInfo, submitDemoRequest } from '~/api'
  import SuccessModal from '~/components/SuccessModal/index.vue'
  import AuthModal from '~/components/AuthModal/index.vue'

  const { currentUser, loginWithGoogle, loginWithGithub } = useFirebaseAuth()

  const reasonForContact = ref('')
  const isOthersSelected = ref(false)
  const countries = ref<{ code: string; name_en: string }[]>([])
  const isCountryDropdownOpen = ref(false)
  const countrySearch = ref('')
  const selectedCountry = ref<{ code: string; name_en: string } | null>(null)
  const containerRef = ref<HTMLElement | null>(null)
  const marketingConsent = ref(false)
  const name = ref('')
  const email = ref('')
  const affiliation = ref('')
  const jobTitle = ref('')
  const isReasonDropdownOpen = ref(false)
  const showSuccessModal = ref(false)
  const showAuthModal = ref(false)
  const authLoading = ref(false)
  const reasonOptions = [
    { value: '', label: 'Please select Reason' },
    {
      value: 'Our AI team expansion faces bottlenecks in screening qualified candidates',
      label: 'Our AI team expansion faces bottlenecks in screening qualified candidates',
    },
    {
      value:
        'High costs from incorrect hiring due to lack of professional assessment in technical recruitment',
      label:
        'High costs from incorrect hiring due to lack of professional assessment in technical recruitment',
    },
    {
      value: 'Need to reduce time and cost of AI talent background verification',
      label: 'Need to reduce time and cost of AI talent background verification',
    },
    {
      value: 'Looking for tools to identify top talent in specific AI specialty areas',
      label: 'Looking for tools to identify top talent in specific AI specialty areas',
    },
    {
      value:
        'Existing recruitment systems fail to effectively evaluate open-source contributions and practical skills',
      label:
        'Existing recruitment systems fail to effectively evaluate open-source contributions and practical skills',
    },
    {
      value: 'Seeking in-depth analysis of candidates collaboration potential with our team',
      label: "Seeking in-depth analysis of candidates' collaboration potential with our team",
    },
    { value: 'Others', label: 'Others' },
  ]

  // Filtered country list
  const filteredCountries = computed(() => {
    if (!countrySearch.value) return countries.value
    const searchLower = countrySearch.value.toLowerCase()
    return countries.value.filter(
      country =>
        country.name_en.toLowerCase().includes(searchLower) ||
        country.code.toLowerCase().includes(searchLower)
    )
  })

  // 获取国家列表
  const fetchCountries = async () => {
    try {
      const res = await getDemoInfo()
      if (res.data) {
        countries.value = res.data.countries
      }
    } catch (error) {
      console.error('Failed to fetch countries:', error)
    }
  }

  // 切换国家下拉框
  const toggleCountryDropdown = () => {
    isCountryDropdownOpen.value = !isCountryDropdownOpen.value
  }

  // 选择国家
  const selectCountry = (country: { code: string; name_en: string }) => {
    selectedCountry.value = country
    isCountryDropdownOpen.value = false
    countrySearch.value = ''
  }

  // 点击外部关闭下拉框
  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.rel')) {
      isCountryDropdownOpen.value = false
      isReasonDropdownOpen.value = false
    }
  }

  onMounted(() => {
    fetchCountries()
    document.addEventListener('click', handleClickOutside)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  // 监听 Reason for contact 的变化
  watch(reasonForContact, newValue => {
    isOthersSelected.value = newValue === 'Others'
  })

  // 监听输入值变化
  watch(
    [name, email, affiliation, jobTitle],
    ([newName, newEmail, newAffiliation, newJobTitle]) => {
      name.value = newName
      email.value = newEmail
      affiliation.value = newAffiliation
      jobTitle.value = newJobTitle
    }
  )

  // 获取选项标签
  const getReasonLabel = (value: string) => {
    const option = reasonOptions.find(opt => opt.value === value)
    return option ? option.label : ''
  }

  // 切换原因下拉框
  const toggleReasonDropdown = () => {
    isReasonDropdownOpen.value = !isReasonDropdownOpen.value
  }

  // 选择原因
  const selectReason = (value: string) => {
    reasonForContact.value = value
    isReasonDropdownOpen.value = false
  }

  // 动态生成验证 schema
  const getSchema = () => {
    return yup.object({
      name: yup.string().required('Username is required.'),
      email: yup.string().email('Invalid email address.').required('Email is required.'),
      affiliation: yup.string().required('Affiliation is required.'),
      // country: yup.string().required('Country is required.'),
      jobTitle: yup.string().required('Job title is required.'),
      // ReasonForContact: yup.string().required('Reason for contact is required.'),
      provide: isOthersSelected.value
        ? yup.string().required('Please provide more details when selecting Others.')
        : yup.string(),
    })
  }

  const schema = getSchema()

  const onSubmit = async (values: any) => {
    // 检查用户是否已登录
    if (!currentUser.value) {
      // 显示登录模态框
      showAuthModal.value = true
      return
    }

    if (!selectedCountry.value?.name_en) {
      console.warn('Please choose country')
      return
    }
    if (!reasonForContact.value) {
      console.warn('Please choose reason for action')
      return
    }
    try {
      const formData = {
        email: values.email,
        affiliation: values.affiliation,
        country: selectedCountry.value?.name_en,
        job_title: values.jobTitle,
        contact_reason: reasonForContact.value,
        additional_details: values.provide || undefined,
        marketing_consent: marketingConsent.value,
      }

      const res = await submitDemoRequest('/api/demo-request', formData, {
        headers: { Userid: currentUser?.value?.uid },
      })

      console.log('Submitting form data:', res)

      if (res?.data?.success) {
        // 显示成功弹窗
        showSuccessModal.value = true
      }
    } catch (error) {
      console.error('Failed to submit demo request:', error)
    }
  }

  // 处理成功弹窗关闭
  const handleSuccessClose = () => {
    // 返回分析页面
    navigateTo('/analysis')
  }

  // 处理认证
  const handleAuth = async (provider: 'google' | 'github' | 'twitter') => {
    authLoading.value = true
    try {
      if (provider === 'google') {
        await loginWithGoogle()
      } else if (provider === 'github') {
        await loginWithGithub()
      }
      // 登录成功后关闭模态框
      showAuthModal.value = false
    } catch (error) {
      console.error('Authentication failed:', error)
    } finally {
      authLoading.value = false
    }
  }
</script>

<style scoped>
  .input {
    @apply bg-white dark:bg-[#292929] min-h-10 w-full outline-none px-3 py-2 mt-1 rounded-2px placeholder-text-gray-600 dark:placeholder-[#7A7A7A] text-black dark:text-[#C6C6C6] border-none truncate;
  }

  .submit-btn {
    @apply rounded-lg mt-7.5 py-3 px-6 transition-colors border-none font-medium;
    background-color: #CB7C5D;
    color: #FFFFFF;
  }

  .submit-btn:hover {
    background-color: #B86A4F;
  }

  .dark .submit-btn {
    background-color: #323232;
    color: #C6C6C6;
  }

  .dark .submit-btn:hover {
    background-color: #404040;
  }

  .agree-checkbox {
    @apply mr-2 wh-14px accent-primary-100;
  }

  .scroller {
    height: 300px;
  }

  /* 添加工具提示样式 */
  .input:hover {
    @apply cursor-text;
  }

  /* 移动端响应式样式 */
  @media (max-width: 768px) {
    .mobile-demo-container {
      @apply px-4 pt-8;
    }
    
    .demo-form-container {
      @apply w-full max-w-none p-4 pb-4;
    }
    
    .demo-form-grid {
      @apply grid-cols-1 gap-x-0 gap-y-4;
    }
    
    .mobile-tooltip {
      @apply w-[90vw] max-w-sm;
    }
    
    .mobile-checkbox-label {
      @apply text-xs leading-relaxed;
    }
    
    .submit-btn {
      @apply w-full mt-6;
    }
  }

  /* 超小屏幕（手机）优化 */
  @media (max-width: 480px) {
    .mobile-demo-container {
      @apply px-2 pt-6;
    }
    
    .demo-form-container {
      @apply p-3 pb-3;
    }
    
    .demo-form-grid {
      @apply gap-y-3;
    }
    
    .input {
      @apply min-h-12 px-4 py-3 text-base;
    }
    
    .mobile-tooltip {
      @apply w-[95vw] max-w-xs text-xs px-4 py-3;
    }
    
    .mobile-checkbox-label {
      @apply text-xs leading-relaxed;
    }
    
    .submit-btn {
      @apply py-4 text-lg font-semibold mt-5;
    }
  }
</style>

<template>
  <div class="sticky top-[30px] z-50 mx-auto" :class="isFullWidthPage ? 'w-full px-6' : 'w-[1200px] max-w-full px-4 md:px-0'">
    <div
      class="h-[70px] header-background px-4 md:px-6 flex items-center justify-between rounded-full shadow-sm hover:shadow-md transition-shadow header-border"
      style="box-shadow: 0px 3px 10px 0px rgba(124, 98, 88, 0.04); --tw-shadow: 0px 3px 10px 0px #7C62580A;"
    >
      <div class="flex items-center gap-6">
        <nuxt-link to="/" class="flex items-center">
          <!-- 使用 ClientOnly 避免 logo 的 hydration 不匹配 -->
          <ClientOnly>
            <img
              :src="currentTheme === 'dark' ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="45"
              :alt="currentTheme === 'dark' ? 'DINQ dark logo' : 'DINQ logo'"
            />
            <template #fallback>
              <img
                src="/image/newlogo1.png"
                width="95"
                height="45"
                alt="DINQ logo"
              />
            </template>
          </ClientOnly>
        </nuxt-link>
        <nav class="desktop-nav">
          <nuxt-link
            to="/analysis"
            class="nav-link"
            :class="{
              'font-bold': $route.path === '/analysis' || $route.path === '/github' || $route.path === '/report',
              'font-normal opacity-70': $route.path !== '/analysis' && $route.path !== '/github' && $route.path !== '/report',
            }"
            active-class="font-bold"
          >
            Analysis
          </nuxt-link>
          <nuxt-link
            to="/search"
            class="nav-link"
            :class="{
              'font-bold': $route.path === '/search',
              'font-normal opacity-70': $route.path !== '/search',
            }"
            active-class="font-bold"
          >
            Search
          </nuxt-link>
          <nuxt-link
            to="/billboard"
            class="nav-link"
            :class="{
              'font-bold': $route.path === '/billboard' || $route.path === '/my-bulletin',
              'font-normal opacity-70': $route.path !== '/billboard' && $route.path !== '/my-bulletin',
            }"
            active-class="font-bold"
          >
            Billboard
          </nuxt-link>
        </nav>
      </div>
      <div class="fx-cer gap-3 desktop-actions">
        <a href="https://x.com/dinq_io" target="_blank" class="flex">
          <button class="media-btn">
            <img 
              :src="currentTheme === 'dark' ? '/image/header-x-dark.svg' : '/image/header-x.svg'" 
              alt="X (Twitter)"
              class="x-icon"
            />
          </button>
        </a>

        <button class="theme-btn" @click="toggleTheme">
          <div
            :class="currentTheme === 'light' ? 'i-carbon:sun' : 'i-carbon:moon'"
            class="text-base"
          ></div>
        </button>
        <div class="relative" ref="upgradeRef" @mouseenter="handleUpgradeHover" @mouseleave="handleUpgradeLeave">
          <button class="upgrade-btn" @click="handleUpgradeClick">
            <div class="upgrade-icon"></div>
            <div class="ml-2">Upgrade</div>
          </button>
          <UpgradeModal
            :visible="showUpgradeModal"
            @update:visible="showUpgradeModal = $event"
            @modal-enter="handleModalEnter"
            @modal-leave="handleModalLeave"
          />
        </div>
        <button class="media-btn" v-if="currentUser" @click="handleLogout" style="display: none;"></button>

        <!-- 使用 ClientOnly 包装认证相关组件以避免 SSR hydration 问题 -->
        <ClientOnly>
          <!-- 认证加载状态 -->
          <div v-if="!authInitialized" class="auth-loading">
            <div class="loading-spinner"></div>
          </div>

          <!-- 已登录用户下拉菜单 -->
          <UserDropdown
            v-else-if="currentUser"
            :user="{
              photoURL: userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL,
              name: userProfile?.display_name || firebaseUserProfile?.display_name || currentUser.displayName,
              username: currentUser.username || currentUser.email || '',
              verified: currentUser.verified || true
            }"
            :open="showUserDropdown"
            @update:open="showUserDropdown = $event"
            @signout="handleLogout"
            @settings="onSettings"
            @verification="onVerification"
          />

          <!-- 未登录用户登录按钮 -->
          <button class="login-btn" @click="showModal = true" v-else>Login</button>

          <!-- SSR fallback: 显示一个占位符避免布局跳动 -->
          <template #fallback>
            <div class="auth-loading">
              <div class="loading-spinner"></div>
            </div>
          </template>
        </ClientOnly>
      </div>

      <button class="mobile-menu-btn" @click="toggleMobileMenu">
        <div class="hamburger" :class="{ 'active': showMobileMenu }">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>
    </div>
  </div>

  <Teleport to="body">
    <div 
      v-if="showMobileMenu" 
      class="mobile-menu-overlay" 
      @click="closeMobileMenu"
    ></div>

    <div class="mobile-menu" :class="{ 'show': showMobileMenu }">
      <div class="mobile-menu-content">
        <div class="mobile-user-section">
          <!-- 使用 ClientOnly 包装移动端认证相关组件 -->
          <ClientOnly>
            <!-- 认证加载状态 -->
            <div v-if="!authInitialized" class="mobile-auth-loading">
              <div class="loading-spinner"></div>
              <span>Loading...</span>
            </div>

            <!-- 已登录用户信息 -->
            <div v-else-if="currentUser" class="mobile-user-info">
              <div class="mobile-avatar">
                <img
                  v-if="userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL"
                  :src="userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL"
                  alt="User avatar"
                  class="mobile-avatar-img"
                />
                <div v-else class="mobile-avatar-placeholder">
                  <div class="i-carbon:user text-2xl text-gray-400"></div>
                </div>
              </div>
              <div class="mobile-user-details">
                <div class="mobile-user-name">
                  {{ userProfile?.display_name || firebaseUserProfile?.display_name || currentUser.displayName || 'User' }}
                </div>
                <div class="mobile-user-email">
                  {{ currentUser.email }}
                </div>
              </div>
            </div>

            <!-- 未登录用户登录区域 -->
            <div v-else class="mobile-login-section">
              <button class="mobile-login-btn" @click="showModal = true; closeMobileMenu()">
                Login / Sign Up
              </button>
            </div>

            <!-- SSR fallback: 移动端占位符 -->
            <template #fallback>
              <div class="mobile-auth-loading">
                <div class="loading-spinner"></div>
                <span>Loading...</span>
              </div>
            </template>
          </ClientOnly>
        </div>

        <div class="mobile-menu-divider"></div>

        <nav class="mobile-nav">
          <nuxt-link to="/analysis" class="mobile-nav-item" @click="closeMobileMenu" 
            :class="{ 'mobile-nav-active': $route.path === '/analysis' || $route.path === '/github' || $route.path === '/report' }">
            <span>Analysis</span>
          </nuxt-link>
          <nuxt-link to="/search" class="mobile-nav-item" @click="closeMobileMenu"
            :class="{ 'mobile-nav-active': $route.path === '/search' }">
            <span>Search</span>
          </nuxt-link>
          <nuxt-link to="/billboard" class="mobile-nav-item" @click="closeMobileMenu"
            :class="{ 'mobile-nav-active': $route.path === '/billboard' || $route.path === '/my-bulletin' }">
            <span>Billboard</span>
          </nuxt-link>
        </nav>

        <div class="mobile-menu-divider"></div>

        <div class="mobile-bottom-actions">
          <!-- 用户相关功能（仅登录用户显示） -->
          <div v-if="currentUser" class="mobile-user-actions">
            <button class="mobile-action-btn" @click="handleMobileSettings">
              <div class="i-carbon:settings text-lg"></div>
              <span>Settings</span>
            </button>
            
            <button class="mobile-action-btn" @click="handleMobileVerification">
              <div class="i-carbon:certificate text-lg"></div>
              <span>Verification</span>
            </button>
          </div>

          <button class="mobile-theme-btn" @click="handleMobileThemeToggle">
            <div :class="currentTheme === 'light' ? 'i-carbon:sun' : 'i-carbon:moon'" class="text-lg"></div>
            <span>{{ currentTheme === 'light' ? 'Light Mode' : 'Dark Mode' }}</span>
          </button>

          <a href="https://x.com/dinq_io" target="_blank" class="mobile-social-btn" @click="closeMobileMenu">
            <span>Follow us on X</span>
          </a>

          <button class="mobile-upgrade-btn" @click="handleMobileUpgradeClick">
            <div class="upgrade-icon"></div>
            <span>Upgrade</span>
          </button>

          <button v-if="currentUser" class="mobile-logout-btn" @click="handleMobileLogout">
            <span>Sign Out</span>
          </button>
        </div>
      </div>
    </div>
  </Teleport>
  
  <AuthModal
    ref="authModal"
    :visible="showModal"
    :loading="loading"
    :provider="currentProvider as 'google' | 'github' | 'twitter'"
    @update:visible="showModal = $event"
    @auth="onAuth"
  />
  
  <!-- Settings Modal -->
  <Teleport to="body">
    <SettingsModal 
      v-if="showSettingsModal" 
      :user="{
        photoURL: userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser?.photoURL,
        name: userProfile?.display_name || firebaseUserProfile?.display_name || currentUser?.displayName,
        username: currentUser?.username || currentUser?.email || '',
        verified: currentUser?.verified || true
      }" 
      :tab="activeTab" 
      @close="closeSettingsModal" 
      @signout="handleLogout" 
    />
  </Teleport>
</template>

<script setup lang="ts">
  import { GLOBAL_CURRENT_THEME } from '~/utils/index'
  import { getCurrentUser, getCurrentFirebaseUser, updateUserInfo } from '@/api/user'
  const { $emitter } = useNuxtApp()
  const { loading, currentUser, authInitialized, loginWithGithub, loginWithGoogle, logout } = useFirebaseAuth()
  const showModal = ref(false)
  const showUpgradeModal = ref(false)
  const showUserDropdown = ref(false)
  const upgradeRef = ref<HTMLElement | null>(null)
  const hoverTimer = ref<NodeJS.Timeout | null>(null)
  const authModal = ref<ComponentPublicInstance<{ resetLoading: () => void }> | null>(null)
  const currentProvider = ref('')
  const router = useRouter()
  const route = useRoute()
  import UserDropdown from '../UserDropdown.vue'
  import UpgradeModal from '../UpgradeModal/index.vue'
  import SettingsModal from '../SettingsModal/index.vue'

  const showMobileMenu = ref(false)

  const userProfile = ref<any>(null)
  const firebaseUserProfile = ref<any>(null)

  const currentTheme = ref('light')
  const showThemeSwitcher = computed(() => {
    return route.path === '/billboard' || route.path === '/my-bulletin'
  })

  const isFullWidthPage = computed(() => {
    return route.path === '/billboard' || route.path === '/my-bulletin'
  })

  // 设置模态框状态
  const showSettingsModal = ref(false)
  const activeTab = ref('settings')

  const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value
    if (showMobileMenu.value) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }

  const closeMobileMenu = () => {
    showMobileMenu.value = false
    document.body.style.overflow = ''
  }

  const handleMobileUpgradeClick = () => {
    closeMobileMenu()
    if (!currentUser.value) {
      showModal.value = true
      return
    }
    showUpgradeModal.value = true
  }

  const handleMobileLogout = () => {
    closeMobileMenu()
    handleLogout()
  }

  // 移动端主题切换处理函数
  const handleMobileThemeToggle = () => {
    toggleTheme()
    closeMobileMenu()
  }

  // 移动端设置处理函数
  const handleMobileSettings = () => {
    onSettings()
    closeMobileMenu()
  }

  // 移动端认证处理函数
  const handleMobileVerification = () => {
    onVerification()
    closeMobileMenu()
  }

  const fetchUserProfile = async () => {
    if (!currentUser.value?.uid) return
    
    try {
      const [userRes, firebaseRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid }),
        getCurrentFirebaseUser({ Userid: currentUser.value.uid })
      ])

      if (userRes.success) {
        userProfile.value = userRes.user
      }
      if (firebaseRes.success) {
        firebaseUserProfile.value = firebaseRes.firebase_user
      }

      if (userRes.success && firebaseRes.success) {
        const updateData = {}
        
        if ((!userProfile.value?.display_name || userProfile.value.display_name === null) &&
            firebaseUserProfile.value?.display_name) {
          updateData.display_name = firebaseUserProfile.value.display_name
        }
        
        if ((!userProfile.value?.email || userProfile.value.email === null) &&
            firebaseUserProfile.value?.email) {
          updateData.email = firebaseUserProfile.value.email
        }
        
        if (Object.keys(updateData).length > 0) {
          try {
            const updateRes = await updateUserInfo(updateData, {
              Userid: currentUser.value.uid
            })
            
            if (updateRes.success) {
              if (updateData.display_name) {
                userProfile.value.display_name = updateData.display_name
                console.log('Auto-synced display_name from Firebase:', updateData.display_name)
              }
              if (updateData.email) {
                userProfile.value.email = updateData.email
                console.log('Auto-synced email from Firebase:', updateData.email)
              }
            }
          } catch (error) {
            console.error('Error auto-syncing user info:', error)
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  watch(() => currentUser.value?.uid, (newUid) => {
    if (newUid) {
      fetchUserProfile()
    } else {
      userProfile.value = null
      firebaseUserProfile.value = null
    }
  }, { immediate: true })



  const applyTheme = (theme: string) => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  const toggleTheme = () => {
    const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    currentTheme.value = newTheme
    if (process.client) {
      localStorage.setItem('theme', newTheme)
    }
    applyTheme(newTheme)
    GLOBAL_CURRENT_THEME.value = newTheme
  }

  const onAuth = async (provider: 'google' | 'github' | 'twitter') => {
    currentProvider.value = provider
    switch (provider) {
      case 'google':
        await loginWithGoogle()
        break
      case 'github':
        await loginWithGithub()
        break
      case 'twitter':
        break
      default:
        break
    }
    if (currentUser.value) {
      showModal.value = false
    }
    authModal.value?.resetLoading()
  }
  const handleLogout = () => {
    logout()
    router.push('/analysis')
  }

  const handleUpgradeClick = () => {
    if (!currentUser.value) {
      showModal.value = true
      return
    }
    showUpgradeModal.value = !showUpgradeModal.value
  }

  const handleUpgradeHover = () => {
    if (!currentUser.value) {
      return
    }
    // 清除任何存在的隐藏定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value)
      hoverTimer.value = null
    }
    // 隐藏用户下拉菜单，显示升级浮窗
    showUserDropdown.value = false
    showUpgradeModal.value = true
  }

  const handleUpgradeLeave = () => {
    if (!currentUser.value) {
      return
    }
    // 设置延迟隐藏，给用户时间移动到 modal 上
    hoverTimer.value = setTimeout(() => {
      showUpgradeModal.value = false
    }, 150)
  }

  const handleModalEnter = () => {
    // 当鼠标进入 modal 时，清除隐藏定时器并保持显示状态
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value)
      hoverTimer.value = null
    }
    showUpgradeModal.value = true
  }

  const handleModalLeave = () => {
    // 当鼠标离开 modal 时，立即隐藏
    showUpgradeModal.value = false
  }

  // 监听用户下拉菜单状态变化，确保同一时间只显示一个浮窗
  watch(showUserDropdown, (newValue) => {
    if (newValue) {
      showUpgradeModal.value = false
    }
  })
  const handleClickOutside = (event: Event) => {
    if (upgradeRef.value && !upgradeRef.value.contains(event.target as Node)) {
      showUpgradeModal.value = false
    }
    // 同时处理用户下拉菜单的点击外部隐藏
    showUserDropdown.value = false
  }

  onMounted(() => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      currentTheme.value = savedTheme
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      currentTheme.value = 'dark'
    }
    applyTheme(currentTheme.value)

    // 事件监听
    $emitter.on('auth', () => {
      showModal.value = true
    })
    $emitter.on('user-profile-updated', () => {
      fetchUserProfile()
    })
    document.addEventListener('click', handleClickOutside)
  })
    onUnmounted(() => {
    $emitter.off('auth', () => {})
    $emitter.off('user-profile-updated', () => {})
    document.removeEventListener('click', handleClickOutside)
    
    // 清理定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value)
    }
    
    if (showMobileMenu.value) {
      document.body.style.overflow = ''
    }
    
    // 组件销毁时确保恢复导航栏z-index
    if (showSettingsModal.value) {
      $emitter.emit('settings-modal', false)
    }
  })

  function onSettings() {
    activeTab.value = 'settings'
    showSettingsModal.value = true
    // 通知导航栏降低z-index
    $emitter.emit('settings-modal', true)
  }
  
  function onVerification() {
    activeTab.value = 'verification'
    showSettingsModal.value = true
    // 通知导航栏降低z-index
    $emitter.emit('settings-modal', true)
  }

  function closeSettingsModal() {
    showSettingsModal.value = false
    // 通知导航栏恢复z-index
    $emitter.emit('settings-modal', false)
  }
</script>

<style scoped lang="scss">
  .nav-link {
    transition: all 0.2s ease;
    font-family: Poppins;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #000000;
    text-decoration: none;
    position: relative;
  }

  .dark .nav-link {
    color: #E3E3E3;
  }

  .nav-link:hover {
    color: #CB7C5D;
  }

  .mobile-menu-btn {
    display: none;
    width: 42px;
    height: 42px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .mobile-menu-btn:hover .hamburger span {
    background: #CB7C5D;
  }

  .hamburger {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 22px;
    height: 18px;
  }

  .hamburger span {
    display: block;
    height: 2px;
    width: 100%;
    background: #333;
    border-radius: 1px;
    transition: all 0.3s ease;
  }

  .dark .hamburger span {
    background: #E3E3E3;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    max-width: 80vw;
    height: 100vh;
    background: #FFFFFF;
    z-index: 9999;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  }

  .mobile-menu.show {
    right: 0;
  }

  .dark .mobile-menu {
    background: #1F1F22;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
  }

  .mobile-menu-content {
    height: 100%;
    overflow-y: auto;
    padding: 24px 0;
    display: flex;
    flex-direction: column;
  }

  .mobile-user-section {
    padding: 0 24px 0 24px;
    margin-bottom: 8px;
  }

  .mobile-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .mobile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }

  .mobile-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .mobile-avatar-placeholder {
    width: 100%;
    height: 100%;
    background: #F3F4F6;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .dark .mobile-avatar-placeholder {
    background: #374151;
  }

  .mobile-user-details {
    flex: 1;
    min-width: 0;
  }

  .mobile-user-name {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dark .mobile-user-name {
    color: #F9FAFB;
  }

  .mobile-user-email {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #6B7280;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dark .mobile-user-email {
    color: #9CA3AF;
  }

  .mobile-login-section {
    text-align: center;
  }

  .mobile-login-btn {
    width: 100%;
    padding: 12px 20px;
    background: #000000;
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .mobile-login-btn:hover {
    background: #333333;
  }

  .dark .mobile-login-btn {
    background: #FFFFFF;
    color: #000000;
  }

  .dark .mobile-login-btn:hover {
    background: #F3F4F6;
  }

  .mobile-menu-divider {
    height: 1px;
    background: #E5E7EB;
    margin: 20px 24px;
  }

  .dark .mobile-menu-divider {
    background: #374151;
  }

  .mobile-nav {
    flex: 1;
    padding: 0 24px;
  }

  .mobile-nav-item {
    display: flex;
    align-items: center;
    padding: 14px 12px;
    border-radius: 8px;
    color: #374151;
    text-decoration: none;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: transparent;
    border: none;
    width: 100%;
    cursor: pointer;
    margin-bottom: 4px;
  }

  .mobile-nav-item:hover {
    background: #F3F4F6;
    color: #CB7C5D;
  }

  .mobile-nav-item.mobile-nav-active {
    background: #FDF0EB;
    color: #CB7C5D;
    font-weight: 700;
  }

  .dark .mobile-nav-item {
    color: #E5E7EB;
  }

  .dark .mobile-nav-item:hover {
    background: #374151;
    color: #CB7C5D;
  }

  .dark .mobile-nav-item.mobile-nav-active {
    background: #2A1A15;
    color: #CB7C5D;
  }

  .mobile-bottom-actions {
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .mobile-user-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 4px;
  }

  .mobile-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px;
    background: transparent;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    color: #374151;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-action-btn:hover {
    background: #F9FAFB;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .dark .mobile-action-btn {
    border-color: #4B5563;
    color: #E5E7EB;
  }

  .dark .mobile-action-btn:hover {
    background: #374151;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .mobile-theme-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px;
    background: transparent;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    color: #374151;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-theme-btn:hover {
    background: #F9FAFB;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .dark .mobile-theme-btn {
    border-color: #4B5563;
    color: #E5E7EB;
  }

  .dark .mobile-theme-btn:hover {
    background: #374151;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .mobile-social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    background: transparent;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    color: #374151;
    text-decoration: none;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .mobile-social-btn:hover {
    background: #F9FAFB;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .dark .mobile-social-btn {
    border-color: #4B5563;
    color: #E5E7EB;
  }

  .dark .mobile-social-btn:hover {
    background: #374151;
    border-color: #CB7C5D;
    color: #CB7C5D;
  }

  .mobile-upgrade-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 20px;
    background: #FDF0EB;
    color: #CB7C5D;
    border: none;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .mobile-upgrade-btn:hover {
    background: #FCE8E0;
  }

  .dark .mobile-upgrade-btn {
    background: #131313;
    color: #E3E3E3;
  }

  .dark .mobile-upgrade-btn:hover {
    background: #1A1A1A;
  }

  .mobile-logout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    background: transparent;
    border: 1px solid #FCA5A5;
    border-radius: 8px;
    color: #DC2626;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-logout-btn:hover {
    background: #FEF2F2;
    border-color: #DC2626;
  }

  .dark .mobile-logout-btn {
    border-color: #DC2626;
    color: #DC2626;
  }

  .dark .mobile-logout-btn:hover {
    background: #450A0A;
    border-color: #DC2626;
  }

  // 桌面端导航样式
  .desktop-nav {
    display: flex;
    align-items: center;
    gap: 24px; // 保持原有的导航项间距
  }

  @media (max-width: 768px) {
    .desktop-nav {
      display: none;
    }
    
    .desktop-actions {
      display: none;
    }
    
    .mobile-menu-btn {
      display: flex;
    }
  }

  .login-btn {
    @apply bg-white rounded-full transition-all;
    width: 120px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    text-align: center;
    color: #000000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .login-btn:hover {
    background-color: #F8F9FA;
  }

  .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .login-btn {
    @apply bg-[#2A2A2A];
    color: #E3E3E3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .dark .login-btn:hover {
    background-color: #353535;
  }

  .dark .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .upgrade-btn {
    @apply bg-#FDF0EB rounded-full transition-all;
    width: 121px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    text-align: center;
    color: #CB7C5D;
    box-shadow: 0 2px 8px rgba(203, 124, 93, 0.1);
  }

  .upgrade-btn:hover {
    background-color: #FCE8E0;
    box-shadow: 0 4px 12px rgba(203, 124, 93, 0.15);
  }

  .dark .upgrade-btn {
    @apply bg-[#131313];
    color: #E3E3E3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .dark .upgrade-btn:hover {
    background-color: #1A1A1A;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .upgrade-icon {
    background-image: url('~/assets/image/upgrade.png');
    background-size: 100%;
    width: 20px;
    height: 20px;
    display: inline-block;
  }
  .dark .upgrade-icon {
    background-image: url('~/assets/image/upgradedark.png');
    background-size: 100%;
    width: 20px;
    height: 20px;
    display: inline-block;
  }

  .theme-btn {
    @apply rounded-full transition-colors bg-white text-gray-500 dark:bg-transparent dark:text-gray-400;
    display: flex;
    align-items: center;
    width: 42px;
    height: 42px;
    justify-content: center;
  }

  .theme-btn:hover {
    background-color: #F8F9FA;
    color: #CB7C5D;
  }

  .dark .theme-btn:hover {
    background-color: #353535;
    color: #CB7C5D;
  }

  .text-base {
    background-size: 100%;
    width: 30px;
    height: 30px;
    display: inline-block;
    padding: 4px;
  }

  .media-btn {
    @apply rounded-full transition-colors text-gray-500 dark:text-gray-400;
    display: flex;
    align-items: center;
    width: 42px;
    height: 42px;
    justify-content: center;
  }

  .media-btn:hover {
    background-color: #F8F9FA;
    color: #CB7C5D;
  }

  .dark .media-btn:hover {
    background-color: #353535;
    color: #CB7C5D;
  }

  .x-icon {
    transition: all 0.2s ease;
    filter: brightness(0) saturate(100%) invert(41%) sepia(12%) saturate(382%) hue-rotate(183deg) brightness(98%) contrast(89%);
  }

  .dark .x-icon {
    filter: brightness(0) saturate(100%) invert(67%) sepia(8%) saturate(434%) hue-rotate(183deg) brightness(90%) contrast(89%);
  }

  .media-btn:hover .x-icon {
    filter: brightness(0) saturate(100%) invert(56%) sepia(36%) saturate(614%) hue-rotate(336deg) brightness(92%) contrast(85%);
  }

  /* 认证加载状态样式 */
  .auth-loading {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    min-width: 80px;
    justify-content: center;
  }

  .mobile-auth-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    gap: 8px;
    color: #666;
    font-size: 14px;
  }

  .dark .mobile-auth-loading {
    color: #ccc;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .dark .loading-spinner {
    border-color: #666;
    border-top-color: #ccc;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Header 样式 - 避免 SSR hydration 不匹配 */
  .header-background {
    background-color: white;
    transition: background-color 0.3s ease;
  }

  .dark .header-background {
    background-color: #1F1F22;
  }

  .header-border {
    border: 1px solid #EEEEEE;
    transition: border-color 0.3s ease;
  }

  .dark .header-border {
    border-color: #27272A;
  }
</style>

export default defineEventHandler(async (event) => {
  // 获取查询参数
  const query = getQuery(event)
  const profileId = query.profile_id as string

  if (!profileId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing profile_id parameter'
    })
  }

  // 获取请求头中的 Authorization
  const authorization = getHeader(event, 'authorization')
  
  if (!authorization) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Missing Authorization header'
    })
  }

  try {
    // 构建目标 URL
    const targetUrl = `https://search.dinq.io/api/v1/talent/email?profile_id=${encodeURIComponent(profileId)}`

    // 转发请求到真实的 API
    const response = await fetch(targetUrl, {
      method: 'GET',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw createError({
        statusCode: response.status,
        statusMessage: `API Error: ${errorText}`
      })
    }

    const data = await response.json()
    return data

  } catch (error: any) {
    console.error('Email API proxy error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: `Internal server error: ${error.message}`
    })
  }
})
